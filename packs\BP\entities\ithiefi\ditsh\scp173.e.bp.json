{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:scp173", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:playing_music": {"type": "bool", "client_sync": false, "default": false}, "ditsh:scp173_state": {"type": "enum", "values": ["mobile", "immobile"], "default": "mobile", "client_sync": true}}}, "component_groups": {"ditsh:music_playing": {"minecraft:timer": {"looping": true, "time": 5, "time_down_event": {"event": "ditsh:maintain_chase_music"}}}, "ditsh:mobile": {"minecraft:movement": {"value": 0.6}, "minecraft:knockback_resistance": {"value": 0.0}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 2.15, "on_attack": {"event": "ditsh:on_kill_player", "target": "self"}}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "target_search_height": -1, "reselect_targets": true, "cooldown": 0, "attack_interval": 0, "must_reach": false, "must_see": false, "reevaluate_description": true, "entity_types": [{"priority": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "max_dist": 256}]}, "minecraft:behavior.hurt_by_target": {"priority": 1, "entity_types": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "reevaluate_description": false, "max_dist": 256}}, "minecraft:behavior.random_look_around": {"priority": 5}, "minecraft:behavior.random_stroll": {"priority": 4}}, "ditsh:immobile": {"minecraft:movement": {"value": 0.0}, "minecraft:knockback_resistance": {"value": 1.0}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:body_rotation_blocked": {}}, "ditsh:hostile": {"minecraft:looked_at": {"search_radius": 24.0, "look_at_locations": [{"location": "head"}, {"location": "body"}, {"location": "feet", "vertical_offset": 0.5}], "set_target": "never", "find_players_only": true, "looked_at_cooldown": 0.1, "field_of_view": 120, "scale_fov_by_distance": false, "line_of_sight_obstruction_type": "collision_for_camera", "looked_at_event": {"event": "ditsh:on_player_start_looking"}, "not_looked_at_event": {"event": "ditsh:on_player_stop_looking"}, "filters": {"test": "actor_health", "subject": "other", "operator": ">", "value": 0}}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ditsh:mobile", "ditsh:hostile"]}, "set_property": {"ditsh:scp173_state": "mobile"}, "queue_command": {"command": "playsound mob.ditsh.scp173.spawn @a ~ ~ ~"}}, "ditsh:on_death": {}, "ditsh:on_kill_player": {"queue_command": {"command": "playsound mob.ditsh.scp173.kill @a ~ ~ ~"}}, "ditsh:on_player_start_looking": {"filters": {"test": "enum_property", "domain": "ditsh:scp173_state", "value": "mobile"}, "add": {"component_groups": ["ditsh:immobile"]}, "remove": {"component_groups": ["ditsh:mobile"]}, "set_property": {"ditsh:scp173_state": "immobile"}}, "ditsh:on_player_stop_looking": {"filters": {"test": "enum_property", "domain": "ditsh:scp173_state", "value": "immobile"}, "add": {"component_groups": ["ditsh:mobile"]}, "remove": {"component_groups": ["ditsh:immobile"]}, "set_property": {"ditsh:scp173_state": "mobile"}}, "ditsh:start_chase_music": {"add": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": true}}, "ditsh:stop_chase_music": {"remove": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": false}}, "ditsh:maintain_chase_music": {}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(1,3)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "scp173", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.6, "height": 2.9}, "minecraft:health": {"value": 40, "max": 40}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true}, "minecraft:attack": {"damage": 20}, "minecraft:follow_range": {"value": 1024, "max": 1024}, "minecraft:jump.static": {}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": false}]}, "minecraft:on_death": {"event": "ditsh:on_death", "target": "self"}, "minecraft:environment_sensor": {"triggers": [{"event": "ditsh:start_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": true}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": false}]}}, {"event": "ditsh:stop_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": false}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": true}]}}]}}}}